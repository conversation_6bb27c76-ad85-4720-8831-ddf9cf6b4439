// 3D Lumbermill Game - Main Game Logic
class LumbermillGame {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.stats = null;

        // Game state
        this.gameState = {
            wood: 0,
            logs: 0,
            money: 1000,
            productionRate: 1,
            isProducing: false,
            level: 1,
            experience: 0,
            experienceToNext: 100,
            upgrades: {
                sawEfficiency: 1,
                treeRegrowth: 1,
                processingSpeed: 1,
                profitMultiplier: 1
            }
        };

        // Game objects
        this.trees = [];
        this.machinery = [];
        this.particles = [];

        // Raycaster for mouse interactions
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();

        this.init();
    }

    init() {
        this.createScene();
        this.createCamera();
        this.createRenderer();
        this.createLights();
        this.createControls();
        this.createStats();
        this.createEnvironment();
        this.createLumbermill();
        this.createForest();
        this.createMachinery();
        this.setupEventListeners();
        this.loadGame();
        this.hideLoading();

        // Start production automatically
        setTimeout(() => {
            this.gameState.isProducing = true;
            console.log('🌲 Lumbermill production started automatically!');
        }, 2000);

        this.animate();
    }

    createScene() {
        this.scene = new THREE.Scene();

        // Create a beautiful sky gradient
        const skyGeometry = new THREE.SphereGeometry(500, 32, 32);
        const skyMaterial = new THREE.ShaderMaterial({
            uniforms: {
                topColor: { value: new THREE.Color(0x87CEEB) },
                bottomColor: { value: new THREE.Color(0xffffff) },
                offset: { value: 33 },
                exponent: { value: 0.6 }
            },
            vertexShader: `
                varying vec3 vWorldPosition;
                void main() {
                    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
                    vWorldPosition = worldPosition.xyz;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform vec3 topColor;
                uniform vec3 bottomColor;
                uniform float offset;
                uniform float exponent;
                varying vec3 vWorldPosition;
                void main() {
                    float h = normalize(vWorldPosition + offset).y;
                    gl_FragColor = vec4(mix(bottomColor, topColor, max(pow(max(h, 0.0), exponent), 0.0)), 1.0);
                }
            `,
            side: THREE.BackSide
        });

        const sky = new THREE.Mesh(skyGeometry, skyMaterial);
        this.scene.add(sky);

        // Add fog for atmospheric effect
        this.scene.fog = new THREE.Fog(0xcccccc, 100, 500);
    }

    createCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );
        this.camera.position.set(30, 25, 30);
        this.camera.lookAt(0, 0, 0);
    }

    createRenderer() {
        this.renderer = new THREE.WebGLRenderer({
            antialias: true,
            alpha: true
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;

        // Add canvas to DOM
        this.renderer.domElement.id = 'gameCanvas';
        document.getElementById('gameContainer').appendChild(this.renderer.domElement);
    }

    createLights() {
        // Ambient light for overall illumination
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(50, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;
        this.scene.add(directionalLight);

        // Hemisphere light for natural outdoor lighting
        const hemisphereLight = new THREE.HemisphereLight(0x87CEEB, 0x8B4513, 0.6);
        this.scene.add(hemisphereLight);

        // Add some atmospheric lighting effects
        const pointLight = new THREE.PointLight(0xFFAA00, 0.5, 50);
        pointLight.position.set(0, 10, 0);
        pointLight.castShadow = true;
        this.scene.add(pointLight);

        // Store lights for potential day/night cycle
        this.lights = {
            directional: directionalLight,
            hemisphere: hemisphereLight,
            point: pointLight
        };
    }

    createControls() {
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.screenSpacePanning = false;
        this.controls.minDistance = 10;
        this.controls.maxDistance = 200;
        this.controls.maxPolarAngle = Math.PI / 2;
    }

    createStats() {
        this.stats = new Stats();
        this.stats.showPanel(0);
        document.body.appendChild(this.stats.dom);
        this.stats.dom.style.position = 'absolute';
        this.stats.dom.style.top = '10px';
        this.stats.dom.style.right = '10px';
    }

    createEnvironment() {
        // Create ground
        const groundGeometry = new THREE.PlaneGeometry(200, 200);
        const groundMaterial = new THREE.MeshLambertMaterial({
            color: 0x3a5f3a,
            transparent: true,
            opacity: 0.8
        });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        this.scene.add(ground);

        // Create dirt paths
        const pathGeometry = new THREE.PlaneGeometry(8, 100);
        const pathMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });

        // Main path to lumbermill
        const mainPath = new THREE.Mesh(pathGeometry, pathMaterial);
        mainPath.rotation.x = -Math.PI / 2;
        mainPath.position.y = 0.01;
        mainPath.receiveShadow = true;
        this.scene.add(mainPath);

        // Cross path
        const crossPath = new THREE.Mesh(pathGeometry, pathMaterial);
        crossPath.rotation.x = -Math.PI / 2;
        crossPath.rotation.z = Math.PI / 2;
        crossPath.position.y = 0.01;
        crossPath.receiveShadow = true;
        this.scene.add(crossPath);

        // Add some rocks and details
        this.createEnvironmentDetails();
    }

    createEnvironmentDetails() {
        // Create rocks scattered around
        for (let i = 0; i < 15; i++) {
            const rockGeometry = new THREE.DodecahedronGeometry(
                Math.random() * 1.5 + 0.5,
                Math.floor(Math.random() * 2)
            );
            const rockMaterial = new THREE.MeshLambertMaterial({
                color: new THREE.Color().setHSL(0, 0, Math.random() * 0.3 + 0.3)
            });
            const rock = new THREE.Mesh(rockGeometry, rockMaterial);

            rock.position.x = (Math.random() - 0.5) * 180;
            rock.position.z = (Math.random() - 0.5) * 180;
            rock.position.y = rockGeometry.parameters.radius * 0.5;

            rock.rotation.x = Math.random() * Math.PI;
            rock.rotation.y = Math.random() * Math.PI;
            rock.rotation.z = Math.random() * Math.PI;

            rock.castShadow = true;
            rock.receiveShadow = true;
            this.scene.add(rock);
        }

        // Create grass patches
        for (let i = 0; i < 30; i++) {
            const grassGeometry = new THREE.ConeGeometry(0.1, Math.random() * 2 + 1, 3);
            const grassMaterial = new THREE.MeshLambertMaterial({
                color: new THREE.Color().setHSL(0.25, 0.7, Math.random() * 0.3 + 0.4)
            });
            const grass = new THREE.Mesh(grassGeometry, grassMaterial);

            grass.position.x = (Math.random() - 0.5) * 190;
            grass.position.z = (Math.random() - 0.5) * 190;
            grass.position.y = grassGeometry.parameters.height * 0.5;

            grass.castShadow = true;
            this.scene.add(grass);
        }
    }

    createLumbermill() {
        const lumbermillGroup = new THREE.Group();

        // Main building structure
        const buildingGeometry = new THREE.BoxGeometry(25, 12, 20);
        const buildingMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
        building.position.y = 6;
        building.castShadow = true;
        building.receiveShadow = true;
        lumbermillGroup.add(building);

        // Roof
        const roofGeometry = new THREE.ConeGeometry(18, 8, 4);
        const roofMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
        const roof = new THREE.Mesh(roofGeometry, roofMaterial);
        roof.position.y = 16;
        roof.rotation.y = Math.PI / 4;
        roof.castShadow = true;
        lumbermillGroup.add(roof);

        // Chimney
        const chimneyGeometry = new THREE.CylinderGeometry(1, 1.2, 8);
        const chimneyMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
        const chimney = new THREE.Mesh(chimneyGeometry, chimneyMaterial);
        chimney.position.set(8, 18, 5);
        chimney.castShadow = true;
        lumbermillGroup.add(chimney);

        // Large entrance door
        const doorGeometry = new THREE.BoxGeometry(6, 8, 0.5);
        const doorMaterial = new THREE.MeshLambertMaterial({ color: 0x2F4F2F });
        const door = new THREE.Mesh(doorGeometry, doorMaterial);
        door.position.set(0, 4, 10.25);
        door.castShadow = true;
        lumbermillGroup.add(door);

        // Windows
        const windowGeometry = new THREE.BoxGeometry(2, 2, 0.2);
        const windowMaterial = new THREE.MeshLambertMaterial({
            color: 0x87CEEB,
            transparent: true,
            opacity: 0.7
        });

        // Side windows
        for (let i = 0; i < 3; i++) {
            const window1 = new THREE.Mesh(windowGeometry, windowMaterial);
            window1.position.set(12.6, 6 + i * 3, -5 + i * 5);
            lumbermillGroup.add(window1);

            const window2 = new THREE.Mesh(windowGeometry, windowMaterial);
            window2.position.set(-12.6, 6 + i * 3, -5 + i * 5);
            lumbermillGroup.add(window2);
        }

        // Storage area
        const storageGeometry = new THREE.BoxGeometry(15, 6, 12);
        const storageMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
        const storage = new THREE.Mesh(storageGeometry, storageMaterial);
        storage.position.set(20, 3, -15);
        storage.castShadow = true;
        storage.receiveShadow = true;
        lumbermillGroup.add(storage);

        // Log pile near storage
        this.createLogPile(lumbermillGroup, 25, 1, -20);
        this.createLogPile(lumbermillGroup, 15, 1, -25);

        this.scene.add(lumbermillGroup);

        // Store reference for later use
        this.lumbermillGroup = lumbermillGroup;
    }

    createLogPile(parent, x, y, z) {
        const logPile = new THREE.Group();

        for (let i = 0; i < 8; i++) {
            const logGeometry = new THREE.CylinderGeometry(0.3, 0.4, 4);
            const logMaterial = new THREE.MeshLambertMaterial({
                color: new THREE.Color().setHSL(0.08, 0.6, Math.random() * 0.3 + 0.3)
            });
            const log = new THREE.Mesh(logGeometry, logMaterial);

            log.position.x = (Math.random() - 0.5) * 4;
            log.position.y = Math.random() * 2 + 1;
            log.position.z = (Math.random() - 0.5) * 4;

            log.rotation.x = (Math.random() - 0.5) * 0.5;
            log.rotation.z = (Math.random() - 0.5) * Math.PI;

            log.castShadow = true;
            log.receiveShadow = true;
            log.userData.type = 'log';

            logPile.add(log);
        }

        logPile.position.set(x, y, z);
        parent.add(logPile);
    }

    createForest() {
        // Create trees around the lumbermill
        const treePositions = [];

        // Generate tree positions avoiding the lumbermill area
        for (let i = 0; i < 80; i++) {
            let x, z;
            let validPosition = false;
            let attempts = 0;

            while (!validPosition && attempts < 50) {
                x = (Math.random() - 0.5) * 180;
                z = (Math.random() - 0.5) * 180;

                // Avoid lumbermill area and paths
                const distanceFromCenter = Math.sqrt(x * x + z * z);
                const nearPath = Math.abs(x) < 6 || Math.abs(z) < 6;
                const nearLumbermill = Math.abs(x) < 35 && Math.abs(z) < 25;

                if (distanceFromCenter > 15 && !nearPath && !nearLumbermill) {
                    validPosition = true;
                }
                attempts++;
            }

            if (validPosition) {
                treePositions.push({ x, z });
            }
        }

        // Create different types of trees
        treePositions.forEach((pos, index) => {
            const treeType = Math.floor(Math.random() * 3);
            let tree;

            switch (treeType) {
                case 0:
                    tree = this.createPineTree(pos.x, pos.z);
                    break;
                case 1:
                    tree = this.createOakTree(pos.x, pos.z);
                    break;
                case 2:
                    tree = this.createBirchTree(pos.x, pos.z);
                    break;
            }

            if (tree) {
                tree.userData.type = 'tree';
                tree.userData.treeId = index;
                this.trees.push(tree);
                this.scene.add(tree);
            }
        });
    }

    createPineTree(x, z) {
        const treeGroup = new THREE.Group();

        // Trunk
        const trunkHeight = Math.random() * 8 + 12;
        const trunkRadius = Math.random() * 0.3 + 0.4;
        const trunkGeometry = new THREE.CylinderGeometry(
            trunkRadius * 0.8,
            trunkRadius,
            trunkHeight,
            8
        );
        const trunkMaterial = new THREE.MeshLambertMaterial({
            color: new THREE.Color().setHSL(0.08, 0.6, 0.25)
        });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.y = trunkHeight / 2;
        trunk.castShadow = true;
        trunk.receiveShadow = true;
        treeGroup.add(trunk);

        // Pine needles (multiple cone layers)
        const layers = 4;
        for (let i = 0; i < layers; i++) {
            const layerHeight = Math.random() * 3 + 4;
            const layerRadius = (layers - i) * 2 + Math.random() * 2;
            const needlesGeometry = new THREE.ConeGeometry(layerRadius, layerHeight, 8);
            const needlesMaterial = new THREE.MeshLambertMaterial({
                color: new THREE.Color().setHSL(0.25, 0.7, Math.random() * 0.2 + 0.2)
            });
            const needles = new THREE.Mesh(needlesGeometry, needlesMaterial);
            needles.position.y = trunkHeight - 2 + i * 2;
            needles.castShadow = true;
            treeGroup.add(needles);
        }

        treeGroup.position.set(x, 0, z);
        treeGroup.userData.treeType = 'pine';
        return treeGroup;
    }

    createOakTree(x, z) {
        const treeGroup = new THREE.Group();

        // Trunk
        const trunkHeight = Math.random() * 6 + 8;
        const trunkRadius = Math.random() * 0.4 + 0.5;
        const trunkGeometry = new THREE.CylinderGeometry(
            trunkRadius * 0.9,
            trunkRadius,
            trunkHeight,
            8
        );
        const trunkMaterial = new THREE.MeshLambertMaterial({
            color: new THREE.Color().setHSL(0.08, 0.5, 0.3)
        });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.y = trunkHeight / 2;
        trunk.castShadow = true;
        trunk.receiveShadow = true;
        treeGroup.add(trunk);

        // Canopy (sphere with some randomness)
        const canopyRadius = Math.random() * 4 + 6;
        const canopyGeometry = new THREE.SphereGeometry(canopyRadius, 8, 6);
        const canopyMaterial = new THREE.MeshLambertMaterial({
            color: new THREE.Color().setHSL(0.25, 0.8, Math.random() * 0.2 + 0.3)
        });
        const canopy = new THREE.Mesh(canopyGeometry, canopyMaterial);
        canopy.position.y = trunkHeight + canopyRadius * 0.7;
        canopy.scale.y = 0.8; // Flatten slightly
        canopy.castShadow = true;
        treeGroup.add(canopy);

        // Add some branches
        for (let i = 0; i < 3; i++) {
            const branchGeometry = new THREE.CylinderGeometry(0.1, 0.15, 2);
            const branchMaterial = new THREE.MeshLambertMaterial({ color: 0x4a4a2a });
            const branch = new THREE.Mesh(branchGeometry, branchMaterial);

            const angle = (i / 3) * Math.PI * 2;
            branch.position.x = Math.cos(angle) * 2;
            branch.position.z = Math.sin(angle) * 2;
            branch.position.y = trunkHeight - 1;
            branch.rotation.z = Math.cos(angle) * 0.5;
            branch.rotation.x = Math.sin(angle) * 0.5;
            branch.castShadow = true;
            treeGroup.add(branch);
        }

        treeGroup.position.set(x, 0, z);
        treeGroup.userData.treeType = 'oak';
        return treeGroup;
    }

    createBirchTree(x, z) {
        const treeGroup = new THREE.Group();

        // Trunk (white birch)
        const trunkHeight = Math.random() * 8 + 10;
        const trunkRadius = Math.random() * 0.2 + 0.3;
        const trunkGeometry = new THREE.CylinderGeometry(
            trunkRadius * 0.9,
            trunkRadius,
            trunkHeight,
            8
        );
        const trunkMaterial = new THREE.MeshLambertMaterial({
            color: new THREE.Color().setHSL(0, 0, 0.9)
        });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.y = trunkHeight / 2;
        trunk.castShadow = true;
        trunk.receiveShadow = true;
        treeGroup.add(trunk);

        // Canopy (more sparse than oak)
        const canopyRadius = Math.random() * 3 + 4;
        const canopyGeometry = new THREE.SphereGeometry(canopyRadius, 8, 6);
        const canopyMaterial = new THREE.MeshLambertMaterial({
            color: new THREE.Color().setHSL(0.25, 0.6, Math.random() * 0.3 + 0.4)
        });
        const canopy = new THREE.Mesh(canopyGeometry, canopyMaterial);
        canopy.position.y = trunkHeight + canopyRadius * 0.6;
        canopy.scale.y = 0.7;
        canopy.castShadow = true;
        treeGroup.add(canopy);

        treeGroup.position.set(x, 0, z);
        treeGroup.userData.treeType = 'birch';
        return treeGroup;
    }

    createMachinery() {
        // Create conveyor belt system
        this.createConveyorBelt();

        // Create saw blade
        this.createSawBlade();

        // Create log processing station
        this.createLogProcessor();

        // Create wood chipper
        this.createWoodChipper();
    }

    createConveyorBelt() {
        const conveyorGroup = new THREE.Group();

        // Main conveyor belt
        const beltGeometry = new THREE.BoxGeometry(20, 0.5, 3);
        const beltMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const belt = new THREE.Mesh(beltGeometry, beltMaterial);
        belt.position.set(0, 1, -5);
        belt.castShadow = true;
        belt.receiveShadow = true;
        conveyorGroup.add(belt);

        // Conveyor rollers
        for (let i = 0; i < 10; i++) {
            const rollerGeometry = new THREE.CylinderGeometry(0.2, 0.2, 3.2);
            const rollerMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
            const roller = new THREE.Mesh(rollerGeometry, rollerMaterial);
            roller.position.set(-9 + i * 2, 0.8, -5);
            roller.rotation.z = Math.PI / 2;
            roller.castShadow = true;
            conveyorGroup.add(roller);
        }

        // Support structure
        const supportGeometry = new THREE.BoxGeometry(1, 3, 1);
        const supportMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });

        for (let i = 0; i < 5; i++) {
            const support = new THREE.Mesh(supportGeometry, supportMaterial);
            support.position.set(-8 + i * 4, 0, -5);
            support.castShadow = true;
            conveyorGroup.add(support);
        }

        conveyorGroup.userData.type = 'machinery';
        conveyorGroup.userData.machineryType = 'conveyor';
        this.machinery.push(conveyorGroup);
        this.scene.add(conveyorGroup);
    }

    createSawBlade() {
        const sawGroup = new THREE.Group();

        // Saw blade
        const bladeGeometry = new THREE.CylinderGeometry(2, 2, 0.1, 32);
        const bladeMaterial = new THREE.MeshLambertMaterial({
            color: 0xC0C0C0,
            transparent: true,
            opacity: 0.8
        });
        const blade = new THREE.Mesh(bladeGeometry, bladeMaterial);
        blade.position.set(5, 4, -5);
        blade.rotation.z = Math.PI / 2;
        blade.castShadow = true;
        sawGroup.add(blade);

        // Saw teeth (simplified)
        for (let i = 0; i < 24; i++) {
            const toothGeometry = new THREE.BoxGeometry(0.1, 0.3, 0.1);
            const toothMaterial = new THREE.MeshLambertMaterial({ color: 0x888888 });
            const tooth = new THREE.Mesh(toothGeometry, toothMaterial);

            const angle = (i / 24) * Math.PI * 2;
            tooth.position.x = Math.cos(angle) * 2.1;
            tooth.position.y = Math.sin(angle) * 2.1;
            tooth.position.z = 0;
            tooth.rotation.z = angle;

            blade.add(tooth);
        }

        // Saw motor housing
        const motorGeometry = new THREE.BoxGeometry(2, 2, 3);
        const motorMaterial = new THREE.MeshLambertMaterial({ color: 0x8B0000 });
        const motor = new THREE.Mesh(motorGeometry, motorMaterial);
        motor.position.set(5, 4, -7);
        motor.castShadow = true;
        sawGroup.add(motor);

        // Support arm
        const armGeometry = new THREE.BoxGeometry(0.5, 6, 0.5);
        const armMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
        const arm = new THREE.Mesh(armGeometry, armMaterial);
        arm.position.set(5, 1, -5);
        arm.castShadow = true;
        sawGroup.add(arm);

        sawGroup.userData.type = 'machinery';
        sawGroup.userData.machineryType = 'saw';
        sawGroup.userData.blade = blade; // Store reference for animation
        this.machinery.push(sawGroup);
        this.scene.add(sawGroup);
    }

    createLogProcessor() {
        const processorGroup = new THREE.Group();

        // Main processing unit
        const unitGeometry = new THREE.BoxGeometry(4, 3, 6);
        const unitMaterial = new THREE.MeshLambertMaterial({ color: 0x2F4F4F });
        const unit = new THREE.Mesh(unitGeometry, unitMaterial);
        unit.position.set(-8, 2.5, -5);
        unit.castShadow = true;
        unit.receiveShadow = true;
        processorGroup.add(unit);

        // Processing blades
        for (let i = 0; i < 3; i++) {
            const bladeGeometry = new THREE.BoxGeometry(0.1, 2, 4);
            const bladeMaterial = new THREE.MeshLambertMaterial({ color: 0xC0C0C0 });
            const blade = new THREE.Mesh(bladeGeometry, bladeMaterial);
            blade.position.set(-8 + (i - 1) * 0.5, 2.5, -5);
            blade.castShadow = true;
            processorGroup.add(blade);
        }

        // Control panel
        const panelGeometry = new THREE.BoxGeometry(1, 1.5, 0.2);
        const panelMaterial = new THREE.MeshLambertMaterial({ color: 0x000080 });
        const panel = new THREE.Mesh(panelGeometry, panelMaterial);
        panel.position.set(-6, 3, -2);
        processorGroup.add(panel);

        // Output chute
        const chuteGeometry = new THREE.CylinderGeometry(0.5, 0.8, 2);
        const chuteMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
        const chute = new THREE.Mesh(chuteGeometry, chuteMaterial);
        chute.position.set(-8, 1, -1);
        chute.rotation.x = Math.PI / 4;
        chute.castShadow = true;
        processorGroup.add(chute);

        processorGroup.userData.type = 'machinery';
        processorGroup.userData.machineryType = 'processor';
        this.machinery.push(processorGroup);
        this.scene.add(processorGroup);
    }

    createWoodChipper() {
        const chipperGroup = new THREE.Group();

        // Main chipper body
        const bodyGeometry = new THREE.CylinderGeometry(2, 2.5, 4);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.set(15, 2, -10);
        body.castShadow = true;
        body.receiveShadow = true;
        chipperGroup.add(body);

        // Input funnel
        const funnelGeometry = new THREE.CylinderGeometry(1.5, 3, 2);
        const funnelMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
        const funnel = new THREE.Mesh(funnelGeometry, funnelMaterial);
        funnel.position.set(15, 5, -10);
        funnel.castShadow = true;
        chipperGroup.add(funnel);

        // Output pipe
        const pipeGeometry = new THREE.CylinderGeometry(0.5, 0.5, 3);
        const pipeMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
        const pipe = new THREE.Mesh(pipeGeometry, pipeMaterial);
        pipe.position.set(17, 2, -10);
        pipe.rotation.z = Math.PI / 2;
        pipe.castShadow = true;
        chipperGroup.add(pipe);

        // Wheels for mobility
        const wheelGeometry = new THREE.CylinderGeometry(0.8, 0.8, 0.3);
        const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x222222 });

        const wheel1 = new THREE.Mesh(wheelGeometry, wheelMaterial);
        wheel1.position.set(13, 0.8, -8);
        wheel1.rotation.z = Math.PI / 2;
        chipperGroup.add(wheel1);

        const wheel2 = new THREE.Mesh(wheelGeometry, wheelMaterial);
        wheel2.position.set(17, 0.8, -8);
        wheel2.rotation.z = Math.PI / 2;
        chipperGroup.add(wheel2);

        chipperGroup.userData.type = 'machinery';
        chipperGroup.userData.machineryType = 'chipper';
        this.machinery.push(chipperGroup);
        this.scene.add(chipperGroup);
    }

    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', () => this.onWindowResize(), false);

        // Mouse events
        this.renderer.domElement.addEventListener('click', (event) => this.onMouseClick(event), false);
        this.renderer.domElement.addEventListener('mousemove', (event) => this.onMouseMove(event), false);

        // Keyboard events
        document.addEventListener('keydown', (event) => this.onKeyDown(event), false);

        // Upgrade panel events
        this.setupUpgradeEvents();
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    onMouseClick(event) {
        // Calculate mouse position in normalized device coordinates
        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

        // Update the picking ray with the camera and mouse position
        this.raycaster.setFromCamera(this.mouse, this.camera);

        // Calculate objects intersecting the picking ray
        const intersects = this.raycaster.intersectObjects(this.scene.children, true);

        if (intersects.length > 0) {
            this.handleObjectClick(intersects[0]);
        }
    }

    onMouseMove(event) {
        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
    }

    onKeyDown(event) {
        switch (event.code) {
            case 'Space':
                event.preventDefault();
                this.toggleProduction();
                break;
            case 'KeyU':
                event.preventDefault();
                this.toggleUpgradePanel();
                break;
        }
    }

    setupUpgradeEvents() {
        document.getElementById('sawEfficiencyUpgrade').addEventListener('click', () => {
            this.purchaseUpgrade('sawEfficiency', 500);
        });

        document.getElementById('treeRegrowthUpgrade').addEventListener('click', () => {
            this.purchaseUpgrade('treeRegrowth', 300);
        });

        document.getElementById('processingSpeedUpgrade').addEventListener('click', () => {
            this.purchaseUpgrade('processingSpeed', 750);
        });

        document.getElementById('profitMultiplierUpgrade').addEventListener('click', () => {
            this.purchaseUpgrade('profitMultiplier', 1000);
        });
    }

    toggleUpgradePanel() {
        const panel = document.getElementById('upgradePanel');
        panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    }

    purchaseUpgrade(upgradeType, baseCost) {
        const cost = baseCost * this.gameState.upgrades[upgradeType];

        if (this.gameState.money >= cost) {
            this.gameState.money -= cost;
            this.gameState.upgrades[upgradeType] += 1;

            console.log(`Purchased ${upgradeType} upgrade for $${cost}`);
            this.addExperience(20);
            this.updateUI();
        } else {
            console.log('Not enough money for upgrade!');
        }
    }

    handleObjectClick(intersection) {
        const object = intersection.object;

        // Handle different object types
        if (object.userData.type === 'tree') {
            this.cutTree(object);
        } else if (object.userData.type === 'machinery') {
            this.operateMachinery(object);
        }
    }

    cutTree(treeObject) {
        // Check if player has enough money for cutting
        const cuttingCost = 10;
        if (this.gameState.money < cuttingCost) {
            console.log('Not enough money to cut tree!');
            return;
        }

        // Deduct cost and add resources
        this.gameState.money -= cuttingCost;
        const logsGained = Math.floor(Math.random() * 3) + 2; // 2-4 logs per tree
        this.gameState.logs += logsGained;

        // Add experience
        this.addExperience(5);

        // Create falling animation
        this.animateTreeFall(treeObject);

        // Remove tree from scene and arrays
        this.scene.remove(treeObject);
        const treeIndex = this.trees.indexOf(treeObject);
        if (treeIndex > -1) {
            this.trees.splice(treeIndex, 1);
        }

        // Create wood chips particle effect
        this.createWoodChipsEffect(treeObject.position);

        console.log(`Tree cut! Gained ${logsGained} logs`);
        this.showFloatingText(`+${logsGained} logs`, treeObject.position, 0x90EE90);
        this.updateUI();

        // Chance to regrow tree after some time
        if (Math.random() < 0.3) {
            setTimeout(() => {
                this.regrowTree(treeObject.position.x, treeObject.position.z);
            }, 10000 + Math.random() * 20000); // 10-30 seconds
        }
    }

    operateMachinery(machineryObject) {
        const machineryType = machineryObject.userData.machineryType;

        switch (machineryType) {
            case 'conveyor':
                this.operateConveyor(machineryObject);
                break;
            case 'saw':
                this.operateSaw(machineryObject);
                break;
            case 'processor':
                this.operateProcessor(machineryObject);
                break;
            case 'chipper':
                this.operateChipper(machineryObject);
                break;
        }
    }

    operateConveyor(conveyorObject) {
        if (this.gameState.logs > 0) {
            this.gameState.logs -= 1;
            this.gameState.wood += 2;
            console.log('Conveyor processed 1 log into 2 wood!');
            this.updateUI();

            // Visual feedback
            this.createProcessingEffect(conveyorObject.position);
        } else {
            console.log('No logs to process!');
        }
    }

    operateSaw(sawObject) {
        const operationCost = 5;
        if (this.gameState.money >= operationCost && this.gameState.logs > 0) {
            this.gameState.money -= operationCost;
            this.gameState.logs -= 1;

            // Apply saw efficiency upgrade
            const baseWood = 4;
            const woodGained = Math.floor(baseWood * (1 + (this.gameState.upgrades.sawEfficiency - 1) * 0.5));
            this.gameState.wood += woodGained;

            // Start saw blade animation
            this.animateSawBlade(sawObject);

            console.log(`Saw processed 1 log into ${woodGained} wood!`);
            this.showFloatingText(`+${woodGained} wood`, sawObject.position, 0xDEB887);
            this.addExperience(3);
            this.updateUI();

            this.createSawdustEffect(sawObject.position);
        } else {
            console.log('Not enough money or logs for saw operation!');
        }
    }

    operateProcessor(processorObject) {
        const operationCost = 15;
        if (this.gameState.money >= operationCost && this.gameState.wood >= 5) {
            this.gameState.money -= operationCost;
            this.gameState.wood -= 5;

            // Apply profit multiplier upgrade
            const baseProfit = 50;
            const profit = Math.floor(baseProfit * (1 + (this.gameState.upgrades.profitMultiplier - 1) * 0.25));
            this.gameState.money += profit;

            console.log(`Processor sold 5 wood for $${profit}!`);
            this.showFloatingText(`+$${profit}`, processorObject.position, 0xFFD700);
            this.addExperience(8);
            this.updateUI();

            this.createProcessingEffect(processorObject.position);
        } else {
            console.log('Not enough money or wood for processing!');
        }
    }

    operateChipper(chipperObject) {
        const operationCost = 8;
        if (this.gameState.money >= operationCost && this.gameState.wood >= 3) {
            this.gameState.money -= operationCost;
            this.gameState.wood -= 3;
            this.gameState.productionRate += 0.1;

            console.log('Chipper increased production rate!');
            this.updateUI();

            this.createChipEffect(chipperObject.position);
        } else {
            console.log('Not enough money or wood for chipping!');
        }
    }

    animateTreeFall(treeObject) {
        // Simple falling animation
        const fallDuration = 1000; // 1 second
        const startRotation = treeObject.rotation.z;
        const endRotation = startRotation + Math.PI / 2;
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / fallDuration, 1);

            treeObject.rotation.z = startRotation + (endRotation - startRotation) * progress;

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        animate();
    }

    animateSawBlade(sawObject) {
        const blade = sawObject.userData.blade;
        if (!blade) return;

        let rotationSpeed = 0.5;
        const duration = 2000; // 2 seconds
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            if (elapsed < duration) {
                blade.rotation.y += rotationSpeed;
                rotationSpeed *= 0.99; // Gradually slow down
                requestAnimationFrame(animate);
            }
        };

        animate();
    }

    regrowTree(x, z) {
        const treeType = Math.floor(Math.random() * 3);
        let tree;

        switch (treeType) {
            case 0:
                tree = this.createPineTree(x, z);
                break;
            case 1:
                tree = this.createOakTree(x, z);
                break;
            case 2:
                tree = this.createBirchTree(x, z);
                break;
        }

        if (tree) {
            tree.userData.type = 'tree';
            tree.userData.treeId = this.trees.length;
            tree.scale.set(0.1, 0.1, 0.1); // Start small

            // Growth animation
            const growthDuration = 3000;
            const startTime = Date.now();

            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / growthDuration, 1);
                const scale = 0.1 + (0.9 * progress);

                tree.scale.set(scale, scale, scale);

                if (progress < 1) {
                    requestAnimationFrame(animate);
                }
            };

            this.trees.push(tree);
            this.scene.add(tree);
            animate();
        }
    }

    // Particle Effects
    createWoodChipsEffect(position) {
        const particleCount = 20;
        const particles = new THREE.Group();

        for (let i = 0; i < particleCount; i++) {
            const chipGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
            const chipMaterial = new THREE.MeshLambertMaterial({
                color: new THREE.Color().setHSL(0.08, 0.6, Math.random() * 0.3 + 0.3)
            });
            const chip = new THREE.Mesh(chipGeometry, chipMaterial);

            chip.position.copy(position);
            chip.position.y += 2;

            // Random velocity
            chip.userData.velocity = new THREE.Vector3(
                (Math.random() - 0.5) * 10,
                Math.random() * 8 + 2,
                (Math.random() - 0.5) * 10
            );
            chip.userData.gravity = -20;
            chip.userData.life = 2000; // 2 seconds
            chip.userData.startTime = Date.now();

            particles.add(chip);
        }

        this.scene.add(particles);
        this.particles.push(particles);

        // Animate particles
        this.animateParticles(particles);
    }

    createSawdustEffect(position) {
        const particleCount = 50;
        const particles = new THREE.Group();

        for (let i = 0; i < particleCount; i++) {
            const dustGeometry = new THREE.SphereGeometry(0.02, 4, 4);
            const dustMaterial = new THREE.MeshLambertMaterial({
                color: new THREE.Color().setHSL(0.08, 0.3, Math.random() * 0.4 + 0.6),
                transparent: true,
                opacity: 0.7
            });
            const dust = new THREE.Mesh(dustGeometry, dustMaterial);

            dust.position.copy(position);
            dust.position.y += 1;

            // Random velocity (more spread out)
            dust.userData.velocity = new THREE.Vector3(
                (Math.random() - 0.5) * 5,
                Math.random() * 3 + 1,
                (Math.random() - 0.5) * 5
            );
            dust.userData.gravity = -5;
            dust.userData.life = 3000; // 3 seconds
            dust.userData.startTime = Date.now();

            particles.add(dust);
        }

        this.scene.add(particles);
        this.particles.push(particles);

        this.animateParticles(particles);
    }

    createProcessingEffect(position) {
        const particleCount = 15;
        const particles = new THREE.Group();

        for (let i = 0; i < particleCount; i++) {
            const sparkGeometry = new THREE.SphereGeometry(0.05, 6, 6);
            const sparkMaterial = new THREE.MeshLambertMaterial({
                color: new THREE.Color().setHSL(0.1, 0.8, Math.random() * 0.3 + 0.7),
                transparent: true,
                opacity: 0.9
            });
            const spark = new THREE.Mesh(sparkGeometry, sparkMaterial);

            spark.position.copy(position);
            spark.position.y += 0.5;

            spark.userData.velocity = new THREE.Vector3(
                (Math.random() - 0.5) * 3,
                Math.random() * 2 + 1,
                (Math.random() - 0.5) * 3
            );
            spark.userData.gravity = -8;
            spark.userData.life = 1500;
            spark.userData.startTime = Date.now();

            particles.add(spark);
        }

        this.scene.add(particles);
        this.particles.push(particles);

        this.animateParticles(particles);
    }

    createChipEffect(position) {
        const particleCount = 30;
        const particles = new THREE.Group();

        for (let i = 0; i < particleCount; i++) {
            const chipGeometry = new THREE.BoxGeometry(0.05, 0.05, 0.05);
            const chipMaterial = new THREE.MeshLambertMaterial({
                color: new THREE.Color().setHSL(0.08, 0.7, Math.random() * 0.2 + 0.4)
            });
            const chip = new THREE.Mesh(chipGeometry, chipMaterial);

            chip.position.copy(position);
            chip.position.y += 1;

            // Chips fly out from chipper
            const angle = (i / particleCount) * Math.PI * 2;
            chip.userData.velocity = new THREE.Vector3(
                Math.cos(angle) * (Math.random() * 3 + 2),
                Math.random() * 4 + 2,
                Math.sin(angle) * (Math.random() * 3 + 2)
            );
            chip.userData.gravity = -15;
            chip.userData.life = 2500;
            chip.userData.startTime = Date.now();

            particles.add(chip);
        }

        this.scene.add(particles);
        this.particles.push(particles);

        this.animateParticles(particles);
    }

    animateParticles(particleGroup) {
        const animate = () => {
            const currentTime = Date.now();
            let allExpired = true;

            particleGroup.children.forEach(particle => {
                const elapsed = currentTime - particle.userData.startTime;
                const deltaTime = 0.016; // Approximate 60fps

                if (elapsed < particle.userData.life) {
                    allExpired = false;

                    // Update position based on velocity and gravity
                    particle.userData.velocity.y += particle.userData.gravity * deltaTime;
                    particle.position.add(
                        particle.userData.velocity.clone().multiplyScalar(deltaTime)
                    );

                    // Fade out over time
                    const lifeProgress = elapsed / particle.userData.life;
                    if (particle.material.transparent) {
                        particle.material.opacity = 1 - lifeProgress;
                    }

                    // Rotate for visual interest
                    particle.rotation.x += 0.1;
                    particle.rotation.y += 0.1;
                    particle.rotation.z += 0.1;

                    // Bounce off ground
                    if (particle.position.y <= 0 && particle.userData.velocity.y < 0) {
                        particle.position.y = 0;
                        particle.userData.velocity.y *= -0.3; // Bounce with energy loss
                        particle.userData.velocity.x *= 0.8; // Friction
                        particle.userData.velocity.z *= 0.8;
                    }
                }
            });

            if (!allExpired) {
                requestAnimationFrame(animate);
            } else {
                // Clean up expired particles
                this.scene.remove(particleGroup);
                const index = this.particles.indexOf(particleGroup);
                if (index > -1) {
                    this.particles.splice(index, 1);
                }
            }
        };

        animate();
    }

    toggleProduction() {
        this.gameState.isProducing = !this.gameState.isProducing;
        console.log('Production:', this.gameState.isProducing ? 'Started' : 'Stopped');
    }

    updateUI() {
        document.getElementById('woodCount').textContent = this.gameState.wood;
        document.getElementById('logCount').textContent = this.gameState.logs;
        document.getElementById('money').textContent = this.gameState.money;
        document.getElementById('productionRate').textContent = this.gameState.productionRate.toFixed(1);
        document.getElementById('level').textContent = this.gameState.level;
        document.getElementById('experience').textContent = this.gameState.experience;
        document.getElementById('experienceToNext').textContent = this.gameState.experienceToNext;

        // Update upgrade availability
        this.updateUpgradeUI();
    }

    updateUpgradeUI() {
        const upgrades = [
            { id: 'sawEfficiencyUpgrade', cost: 500 * this.gameState.upgrades.sawEfficiency },
            { id: 'treeRegrowthUpgrade', cost: 300 * this.gameState.upgrades.treeRegrowth },
            { id: 'processingSpeedUpgrade', cost: 750 * this.gameState.upgrades.processingSpeed },
            { id: 'profitMultiplierUpgrade', cost: 1000 * this.gameState.upgrades.profitMultiplier }
        ];

        upgrades.forEach(upgrade => {
            const element = document.getElementById(upgrade.id);
            if (element) {
                const costElement = element.querySelector('.upgrade-cost');
                costElement.textContent = `Cost: $${upgrade.cost}`;

                if (this.gameState.money >= upgrade.cost) {
                    element.classList.remove('disabled');
                } else {
                    element.classList.add('disabled');
                }
            }
        });
    }

    addExperience(amount) {
        this.gameState.experience += amount;

        // Check for level up
        while (this.gameState.experience >= this.gameState.experienceToNext) {
            this.gameState.experience -= this.gameState.experienceToNext;
            this.gameState.level++;
            this.gameState.experienceToNext = Math.floor(this.gameState.experienceToNext * 1.5);

            // Level up bonus
            this.gameState.money += this.gameState.level * 100;
            console.log(`Level up! Now level ${this.gameState.level}. Bonus: $${this.gameState.level * 100}`);
        }

        this.updateUI();
    }

    hideLoading() {
        const loadingElement = document.getElementById('loading');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        this.stats.begin();

        // Update controls
        this.controls.update();

        // Update game logic
        this.update();

        // Render the scene
        this.renderer.render(this.scene, this.camera);

        this.stats.end();
    }

    update() {
        // Animate machinery
        this.animateMachinery();

        // Animate trees (wind effect)
        this.animateTrees();

        // Create ambient particles occasionally
        if (Math.random() < 0.005) {
            this.createAmbientParticles();
        }

        // Update production
        if (this.gameState.isProducing) {
            const productionChance = 0.02 * this.gameState.upgrades.processingSpeed; // Increased frequency
            if (Math.random() < productionChance) {
                this.gameState.wood += this.gameState.productionRate;
                this.updateUI();

                // Create visual feedback for production
                if (this.machinery.length > 0) {
                    const randomMachine = this.machinery[Math.floor(Math.random() * this.machinery.length)];
                    this.createProcessingEffect(randomMachine.position);
                }
            }
        }

        // Auto-save progress periodically
        if (Math.random() < 0.001) { // Very low chance per frame
            this.autoSave();
        }

        // Simple day/night cycle
        this.updateDayNightCycle();
    }

    animateMachinery() {
        const time = Date.now() * 0.001;

        // Animate conveyor belts
        this.machinery.forEach(machine => {
            if (machine.userData.machineryType === 'conveyor') {
                // Rotate conveyor rollers
                machine.children.forEach(child => {
                    if (child.geometry && child.geometry.type === 'CylinderGeometry') {
                        child.rotation.x += 0.02;
                    }
                });
            } else if (machine.userData.machineryType === 'saw') {
                // Continuous saw blade rotation when producing
                const blade = machine.userData.blade;
                if (blade) {
                    if (this.gameState.isProducing) {
                        blade.rotation.y += 0.1;
                    } else {
                        blade.rotation.y += 0.01; // Slow idle rotation
                    }
                }
            } else if (machine.userData.machineryType === 'chipper') {
                // Gentle bobbing motion for chipper
                if (!machine.userData.originalY) {
                    machine.userData.originalY = machine.position.y;
                }
                machine.position.y = machine.userData.originalY + 0.1 * Math.sin(time * 2);
            }
        });
    }

    animateTrees() {
        const time = Date.now() * 0.001;

        // Add gentle swaying motion to trees
        this.trees.forEach((tree, index) => {
            if (tree && tree.position) {
                // Different trees sway at slightly different rates
                const swaySpeed = 0.5 + (index % 3) * 0.2;
                const swayAmount = 0.02 + (index % 2) * 0.01;

                tree.rotation.z = Math.sin(time * swaySpeed + index) * swayAmount;

                // Slight rotation variation
                tree.rotation.y = tree.userData.originalRotationY || 0;
                tree.rotation.y += Math.sin(time * 0.3 + index) * 0.005;

                if (!tree.userData.originalRotationY) {
                    tree.userData.originalRotationY = tree.rotation.y;
                }
            }
        });
    }

    autoSave() {
        const saveData = {
            gameState: this.gameState,
            timestamp: Date.now()
        };
        localStorage.setItem('lumbermillSave', JSON.stringify(saveData));
        console.log('Game auto-saved');
    }

    loadGame() {
        const saveData = localStorage.getItem('lumbermillSave');
        if (saveData) {
            try {
                const parsed = JSON.parse(saveData);
                if (parsed.gameState) {
                    // Merge saved state with current state
                    Object.assign(this.gameState, parsed.gameState);
                    this.updateUI();
                    console.log('Game loaded from save');
                }
            } catch (e) {
                console.log('Failed to load save data');
            }
        }
    }

    updateDayNightCycle() {
        const time = Date.now() * 0.0005; // Faster cycle for visibility
        const dayProgress = (Math.sin(time) + 1) / 2; // 0 to 1

        if (this.lights) {
            // Adjust lighting based on time of day
            const intensity = 0.2 + dayProgress * 0.8; // 0.2 to 1.0
            this.lights.directional.intensity = intensity;
            this.lights.hemisphere.intensity = 0.3 + dayProgress * 0.5;

            // Change sky color more dramatically
            const hue = 0.6 + (1 - dayProgress) * 0.1; // Blue to purple at night
            const saturation = 0.5 + dayProgress * 0.3;
            const lightness = 0.2 + dayProgress * 0.6;

            const skyColor = new THREE.Color().setHSL(hue, saturation, lightness);
            this.scene.fog.color = skyColor;

            // Update point light (like a lamp)
            this.lights.point.intensity = 0.3 + (1 - dayProgress) * 0.7; // Brighter at night
        }
    }

    showFloatingText(text, position, color = 0xFFFFFF) {
        // Create text geometry (simplified approach using a plane with text texture)
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 256;
        canvas.height = 64;

        context.fillStyle = `#${color.toString(16).padStart(6, '0')}`;
        context.font = 'bold 24px Arial';
        context.textAlign = 'center';
        context.fillText(text, 128, 40);

        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.MeshBasicMaterial({
            map: texture,
            transparent: true,
            alphaTest: 0.1
        });
        const geometry = new THREE.PlaneGeometry(4, 1);
        const textMesh = new THREE.Mesh(geometry, material);

        textMesh.position.copy(position);
        textMesh.position.y += 5;
        textMesh.lookAt(this.camera.position);

        this.scene.add(textMesh);

        // Animate floating text
        const startTime = Date.now();
        const duration = 2000;

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / duration;

            if (progress < 1) {
                textMesh.position.y = position.y + 5 + progress * 3;
                textMesh.material.opacity = 1 - progress;
                textMesh.lookAt(this.camera.position);
                requestAnimationFrame(animate);
            } else {
                this.scene.remove(textMesh);
                textMesh.material.dispose();
                textMesh.geometry.dispose();
                texture.dispose();
            }
        };

        animate();
    }

    createAmbientParticles() {
        // Create floating dust/pollen particles
        const particleCount = 5;
        const particles = new THREE.Group();

        for (let i = 0; i < particleCount; i++) {
            const particleGeometry = new THREE.SphereGeometry(0.02, 4, 4);
            const particleMaterial = new THREE.MeshLambertMaterial({
                color: new THREE.Color().setHSL(0.1, 0.3, Math.random() * 0.5 + 0.5),
                transparent: true,
                opacity: 0.6
            });
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);

            // Random starting position
            particle.position.set(
                (Math.random() - 0.5) * 100,
                Math.random() * 20 + 5,
                (Math.random() - 0.5) * 100
            );

            // Gentle floating motion
            particle.userData.velocity = new THREE.Vector3(
                (Math.random() - 0.5) * 0.5,
                Math.random() * 0.2 + 0.1,
                (Math.random() - 0.5) * 0.5
            );
            particle.userData.life = 10000 + Math.random() * 10000; // 10-20 seconds
            particle.userData.startTime = Date.now();

            particles.add(particle);
        }

        this.scene.add(particles);
        this.particles.push(particles);

        // Animate ambient particles
        this.animateAmbientParticles(particles);
    }

    animateAmbientParticles(particleGroup) {
        const animate = () => {
            const currentTime = Date.now();
            let allExpired = true;

            particleGroup.children.forEach(particle => {
                const elapsed = currentTime - particle.userData.startTime;
                const deltaTime = 0.016;

                if (elapsed < particle.userData.life) {
                    allExpired = false;

                    // Gentle floating motion
                    particle.position.add(
                        particle.userData.velocity.clone().multiplyScalar(deltaTime)
                    );

                    // Gentle bobbing
                    particle.position.y += Math.sin(currentTime * 0.001 + particle.position.x) * 0.01;

                    // Fade out over time
                    const lifeProgress = elapsed / particle.userData.life;
                    particle.material.opacity = 0.6 * (1 - lifeProgress);

                    // Remove if too far or too low
                    if (particle.position.y < 0 ||
                        Math.abs(particle.position.x) > 150 ||
                        Math.abs(particle.position.z) > 150) {
                        particle.userData.life = 0; // Mark for removal
                    }
                }
            });

            if (!allExpired) {
                requestAnimationFrame(animate);
            } else {
                // Clean up expired particles
                this.scene.remove(particleGroup);
                const index = this.particles.indexOf(particleGroup);
                if (index > -1) {
                    this.particles.splice(index, 1);
                }
            }
        };

        animate();
    }
}

// Initialize the game when the page loads
window.addEventListener('load', () => {
    const game = new LumbermillGame();
    window.game = game; // Make game accessible globally for debugging
});
