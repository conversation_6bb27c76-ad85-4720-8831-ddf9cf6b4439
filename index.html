<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Lumbermill Game</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c5530, #1a3d1f);
            overflow: hidden;
            color: white;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #gameCanvas {
            display: block;
            cursor: grab;
        }

        #gameCanvas:active {
            cursor: grabbing;
        }

        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(5px);
        }

        #ui h1 {
            color: #8fbc8f;
            margin-bottom: 10px;
            font-size: 24px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .stat {
            margin: 5px 0;
            font-size: 16px;
        }

        .stat-value {
            color: #90EE90;
            font-weight: bold;
        }

        #controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(5px);
        }

        .control-info {
            margin: 3px 0;
            font-size: 14px;
            color: #cccccc;
        }

        #upgradePanel {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(5px);
            max-width: 250px;
        }

        #upgradePanel h2 {
            color: #8fbc8f;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .upgrade-item {
            margin: 8px 0;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .upgrade-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .upgrade-item.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .upgrade-name {
            font-weight: bold;
            color: #90EE90;
        }

        .upgrade-cost {
            color: #FFD700;
            font-size: 12px;
        }

        .upgrade-description {
            color: #cccccc;
            font-size: 11px;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 200;
            text-align: center;
            background: rgba(0, 0, 0, 0.8);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #8fbc8f;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
    <div id="gameContainer">
        <div id="loading">
            <div class="spinner"></div>
            <h2>Loading Lumbermill...</h2>
            <p>Preparing the forest and machinery</p>
        </div>

        <div id="ui">
            <h1>🌲 Lumbermill Tycoon</h1>
            <div class="stat">Level: <span class="stat-value" id="level">1</span></div>
            <div class="stat">XP: <span class="stat-value" id="experience">0</span>/<span class="stat-value"
                    id="experienceToNext">100</span></div>
            <div class="stat">Wood: <span class="stat-value" id="woodCount">0</span></div>
            <div class="stat">Logs: <span class="stat-value" id="logCount">0</span></div>
            <div class="stat">Money: $<span class="stat-value" id="money">1000</span></div>
            <div class="stat">Production Rate: <span class="stat-value" id="productionRate">1</span>/sec</div>
        </div>

        <div id="upgradePanel">
            <h2>🔧 Upgrades</h2>
            <div class="upgrade-item" id="sawEfficiencyUpgrade">
                <div class="upgrade-name">Saw Efficiency</div>
                <div class="upgrade-cost">Cost: $500</div>
                <div class="upgrade-description">Increase saw output by 50%</div>
            </div>
            <div class="upgrade-item" id="treeRegrowthUpgrade">
                <div class="upgrade-name">Tree Regrowth</div>
                <div class="upgrade-cost">Cost: $300</div>
                <div class="upgrade-description">Trees regrow 25% faster</div>
            </div>
            <div class="upgrade-item" id="processingSpeedUpgrade">
                <div class="upgrade-name">Processing Speed</div>
                <div class="upgrade-cost">Cost: $750</div>
                <div class="upgrade-description">Machinery works 30% faster</div>
            </div>
            <div class="upgrade-item" id="profitMultiplierUpgrade">
                <div class="upgrade-name">Profit Multiplier</div>
                <div class="upgrade-cost">Cost: $1000</div>
                <div class="upgrade-description">Increase all profits by 25%</div>
            </div>
        </div>

        <div id="controls">
            <div class="control-info"><strong>Controls:</strong></div>
            <div class="control-info">🖱️ Mouse: Look around</div>
            <div class="control-info">🖱️ Click: Cut trees / Operate machinery</div>
            <div class="control-info">⌨️ WASD: Move camera</div>
            <div class="control-info">⌨️ Space: Start/Stop production</div>
            <div class="control-info">⌨️ U: Toggle upgrade panel</div>
        </div>
    </div>

    <!-- Three.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <!-- OrbitControls for camera movement -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <!-- Stats for performance monitoring -->
    <script>
        (function () {
            var script = document.createElement('script');
            script.onload = function () {
                var stats = new Stats();
                document.body.appendChild(stats.dom);
                requestAnimationFrame(function loop() {
                    stats.update();
                    requestAnimationFrame(loop);
                });
            };
            script.src = 'https://mrdoob.github.io/stats.js/build/stats.min.js';
            document.head.appendChild(script);
        })();
    </script>

    <!-- Main Game Script -->
    <script src="game.js"></script>
</body>

</html>